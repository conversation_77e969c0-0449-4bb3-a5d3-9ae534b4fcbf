version: "3.8"

services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.5.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181" # Cổng cho Zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  broker:
    image: confluentinc/cp-kafka:7.5.0
    hostname: broker
    container_name: broker
    ports:
      - "9092:9092" # Cổng cho Kafka Broker
      - "9093:9093" # Cổng nội bộ cho giao tiếp giữa các container
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: "zookeeper:2181"
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://broker:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_CONFLUENT_LICENSE_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_CONFLUENT_BALANCER_TOPIC_REPLICATION_FACTOR: 1
    volumes:
      - broker-data:/var/lib/kafka/data # Lưu trữ dữ liệu Kafka

  kafdrop:
    image: obsidiandynamics/kafdrop:latest
    hostname: kafdrop
    container_name: kafdrop
    ports:
      - "9000:9000" # Cổng cho Kafdrop UI
    depends_on:
      - broker
    environment:
      KAFKA_BROKERCONNECT: broker:29092 # Kết nối đến Kafka Broker
      JVM_OPTS: "-Xms32M -Xmx128M" # Tùy chỉnh bộ nhớ cho Kafdrop

volumes:
  broker-data: # Định nghĩa volume để lưu trữ dữ liệu Kafka một cách bền vững
